import discord
from discord import app_commands
from discord.ext import tasks, commands
import datetime as dt
import pytz
import random
from discord.ui import <PERSON><PERSON>, View
import asyncio
import time
from typing import Literal, Tu<PERSON>
from typing import Optional, List




intents = discord.Intents.default()
client = discord.Client(intents=intents)
Adrenalin = app_commands.CommandTree(client)
lock = asyncio.Lock()

TIMEZONE = pytz.timezone('Europe/Berlin')
guild_id1 = 894976581561384970 # Adrenalin
cooldown = time.time()

   
@Adrenalin.command(name="verifymsg", description="admin command only.", guild=discord.Object(id=guild_id1))
async def verifymsg(interaction):
    if interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("OK, ich schicke die Ankündigung.", ephemeral=True)
        title1 = '>>> We do not tolerate cheating, hacking, or any other illegal activity on our server. Please read the Discord Terms of Service and Community Guidelines carefully before joining our server. These guidelines are in place to ensure a safe and enjoyable experience for all members.\n\nCheating, hacking, carding, illigale promotions, ads and other illegal activities are not allowed on this server.\n\nhttps://discord.com/terms\nhttps://discord.com/guidelines\n\n'
        embed = discord.Embed(
        title="**User Verification**",
        description=title1,
        color=0xdf5e0f)
        embed.set_image(url="https://media.discordapp.net/attachments/1096426641007317032/1386397886278795316/discordbanner.png?ex=68598f26&is=68583da6&hm=8fed5e4b1bbb39581bb3e7d64d7a58b0afb4a760def1ac91c9406e5522d6e8a2&=&format=webp&quality=lossless&width=1882&height=394")
        embed.set_footer(text=f"Adrenalin © 2025 | Dominate the leaderboard’s", icon_url="https://media.discordapp.net/attachments/1096426641007317032/1386349984097632276/logo.png?ex=********&is=********&hm=1c763f136586efb7ae5382813b2574d85bd507056d1ca527bc9e9f55d2bc4586&=&format=webp&quality=lossless&width=400&height=400")
        button2 = Button(label="Verify now!", style=discord.ButtonStyle.red, url="https://restorecord.com/verify/Adrenalin")
        view = View()
        view.add_item(button2)
        await interaction.channel.send(embed=embed, view=view)
    else:
        embed = discord.Embed(description="Sorry, you do not have admin permissions to use this command.", color=0xdf5e0f)
        await interaction.response.send_message(embed=embed, ephemeral=True)
      


@Adrenalin.command(name="accounts", description="Embed command!", guild=discord.Object(id=guild_id1))
async def accountsdxfdf(interaction):
    if interaction.user.guild_permissions.administrator:
        await interaction.response.send_message(f"Done!", ephemeral=True) 
        title1 = '>>> **General:**\n[+] Phone verified\n[+] Email verified\n[+] Game ready\n'
        title2 = '**Accounts:**\n[+] Fresh Battlenet\n[+] Fresh Steam\n[+] FA MW3 account\n[+] NFA MW3 account\n'
        title3 = '**Purchase:**\n[+] https://Adrenalin.sellsn.io/\n'

        embed = discord.Embed(
            title="**Gaming Accounts**",
            description=title1 + "\n" + title2 + "\n" + title3,
            color=0xdf5e0f)
        embed.set_image(url="https://media.discordapp.net/attachments/1096426641007317032/1386350012736471270/accounts.jpg?ex=********&is=********&hm=7e49cddbae3c69d5ee971ad08d95e81d1b9bcbde2bd710fa9118a365e442812c&=&format=webp&width=1462&height=974")
        embed.set_footer(text=f"Adrenalin © 2025 | Dominate the leaderboard’s", icon_url="https://media.discordapp.net/attachments/1096426641007317032/1386349984097632276/logo.png?ex=********&is=********&hm=1c763f136586efb7ae5382813b2574d85bd507056d1ca527bc9e9f55d2bc4586&=&format=webp&quality=lossless&width=400&height=400")
        button2 = Button(label="Explore store!", style=discord.ButtonStyle.red, url="https://Adrenalin.sellsn.io/")
        view = View()
        view.add_item(button2)
        await interaction.channel.send(embed=embed, view=view)
    else:
        embed = discord.Embed(description=">>> Sorry, you do not have admin permissions to use this command.", color=0xFF0000)
        await interaction.response.send_message(embed=embed, ephemeral=True)


@Adrenalin.command(name="store", description="Embed command!", guild=discord.Object(id=guild_id1))
async def store(interaction):
    if interaction.user.guild_permissions.administrator:
        await interaction.response.send_message(f"Done!", ephemeral=True) 

        title1 = '>>> **Welcome to Adrenalin Community, your premier provider of premium products for gamers looking to gain an edge. We offer reliable and secure solutions that enhance your gaming experience and help you dominate in your favorite games.**\n'
        title2 = '**Why choose Adrenalin?**\n[+] Tested & trusted in the community.\n[+] Minimal risk with max effectiveness.\n[+] First-class support for all your questions and issues.\n'
        title3 = '**Our Payment Options**\n[+] Credit/Debit Cards > Ticket\n[+] Paypal friends and family > Store\n[+] Litecoin > Store\n[+] Bitcoin > Store\n'
        
        embed = discord.Embed(
            title="**Adrenalin Store**",
            description=title1 + "\n" + title2 + "\n" + title3,
            color=0xdf5e0f)
        embed.set_image(url="https://media.discordapp.net/attachments/1096426641007317032/1386397886278795316/discordbanner.png?ex=68598f26&is=68583da6&hm=8fed5e4b1bbb39581bb3e7d64d7a58b0afb4a760def1ac91c9406e5522d6e8a2&=&format=webp&quality=lossless&width=1882&height=394")
        embed.set_footer(text=f"Adrenalin © 2025 | Dominate the leaderboard’s", icon_url="https://media.discordapp.net/attachments/1096426641007317032/1386349984097632276/logo.png?ex=********&is=********&hm=1c763f136586efb7ae5382813b2574d85bd507056d1ca527bc9e9f55d2bc4586&=&format=webp&quality=lossless&width=400&height=400")
        button2 = Button(label="Explore store!", style=discord.ButtonStyle.red, url="https://Adrenalin.sellsn.io/")
        view = View()
        view.add_item(button2)
        await interaction.channel.send(embed=embed, view=view)
    else:
        embed = discord.Embed(description="Sorry, you do not have admin permissions to use this command.", color=0xFF0000)
        await interaction.response.send_message(embed=embed, ephemeral=True)



@Adrenalin.command(name="infinity", description="Embed command!", guild=discord.Object(id=guild_id1))
async def b06astral(interaction):
    if interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("Done!", ephemeral=True)

        embed_description = (
            "🚀 **System Requirements**\n"
            "• CPU: Intel or AMD\n"
            "• Cheat Type: External\n"
            "• OS: Windows 10/11/All Versions\n\n"

            "🎮 **Supported Games**\n"
            "• Call of Duty: Black Ops 6 & Warzone\n\n"

            "🕹️ **Controller Support**\n"
            "• Native controller compatibility for effortless gameplay.\n\n"

            "🛡️ **Security & Compatibility**\n"
            "• HWID Spoofing and anticheat blocker included for protection.\n\n"

            "**🎯 Aimbot Excellence**\n"
            "• Toggleable aimbot with enemy lock-on\n"
            "• FOV circle with adjustable size\n"
            "• Custom bone selection\n"
            "• Visible check for precision\n"
            "• Adjustable smoothness & aimbot distance\n"
            "• Assignable hotkey\n"
            "• Aim at downed players\n\n"

            "**🔍 Enhanced ESP & Visuals**\n"
            "• Skeleton player models\n"
            "• Distance, health bar, player names\n"
            "• 2D box, corner, and fill box\n"
            "• Snaplines, team check, visible-only mode\n"
            "• Customizable ESP thickness & color palette\n\n"

            "**💰 Loot ESP**\n"
            "• Toggle loot visuals on/off\n"
            "• Distance and rarity filtering\n"
            "• Cash, bags, cases, ammo\n"
            "• Boxes, weapons, gas mask, killstreaks\n\n"

            "**📡 Advanced Radar**\n"
            "• Fully configurable radar overlay\n"
            "• Show enemies, teammates, height arrows\n"
            "• Customizable position, size, lines, and border\n\n"

            "**🧠 Lobby Tracker**\n"
            "• Real-time player names, health, distance\n"
            "• Team IDs, player ping, levels, and K/D ratio\n\n"

            "**⚙️ Pro Settings**\n"
            "• Save & load up to 3 configs\n"
            "• Hide overlay instantly\n"
            "• VSync menu for ultra-smooth performance\n\n"

            "**💵 Purchase:**\n"
            "• Day License: 6€\n"
            "• Week License: 15€\n"
            "• Month License: 30€\n"
            "• https://Adrenalin.sellsn.io/\n"
        )

        embed = discord.Embed(
            title="**BO6 Infinity**",
            description=embed_description,
            color=0xdf5e0f
        )

        embed.set_image(url="https://media.discordapp.net/attachments/1096426641007317032/1386349993614639104/infinity.jpg?ex=6859628b&is=6858110b&hm=9c2b4ffc53cfb633bba9da02ed70bc4ceccd2d223d2c8ff85c34cca275afb22e&=&format=webp&width=1462&height=974")
        embed.set_footer(
            text="Adrenalin © 2025 | Dominate the leaderboard’s",
            icon_url="https://media.discordapp.net/attachments/1096426641007317032/1386349984097632276/logo.png?ex=********&is=********&hm=1c763f136586efb7ae5382813b2574d85bd507056d1ca527bc9e9f55d2bc4586&=&format=webp&quality=lossless&width=400&height=400"
        )

        button = Button(label="Explore store!", style=discord.ButtonStyle.red, url="https://Adrenalin.sellsn.io/")
        view = View()
        view.add_item(button)

        await interaction.channel.send(embed=embed, view=view)
    else:
        error_embed = discord.Embed(
            description="Sorry, you do not have admin permissions to use this command.",
            color=0xFF0000
        )
        await interaction.response.send_message(embed=error_embed, ephemeral=True)




@client.event
async def on_ready():

    await Adrenalin.sync(guild=discord.Object(id=guild_id1))

    activities = [
        discord.Activity(name="Infinity 🔥", type=discord.ActivityType.playing),
        discord.Activity(name="https://adrenalin.sellsn.io/", type=discord.ActivityType.playing),
    ]
    
    print(f"Logged in as {client.user} (ID: {client.user.id})")
    
    @tasks.loop(seconds=3)  
    async def rotate_activity():
        activity = activities[rotate_activity.current_loop % len(activities)]
        await client.change_presence(status=discord.Status.dnd, activity=activity)

    rotate_activity.start()
    print("Bot is ready")
    
client.run("MTA2NjY0NjkwNDg5NDY3Mjk0Nw.GjnU3O.uv1eH4h-MZSO3F0Fz0j5g0Pwwf59zkKxQDUlGI")
# main   client.run("MTA2NjY0NjkwNDg5NDY3Mjk0Nw.GjnU3O.uv1eH4h-MZSO3F0Fz0j5g0Pwwf59zkKxQDUlGI")
# test   client.run('MTAyMzUzMDY4NTcwNjgxMzUzMQ.GWamg1.9xHWbwSb1uIbK2ltYMZUh7UjqidT1BDln1L7FU')