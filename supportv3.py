import discord
from discord.ext import  tasks, commands
from discord import app_commands
from discord.ui import <PERSON><PERSON>, View

MAIN_SERVER = 894976581561384970
TICKET_CHANNEL = 1056703440216133774
SUPPORT_SERVER = 1057299183037595718
TICKET_CATEGORY = 1302587417466765312
ALLOWED_SERVERS = [MAIN_SERVER, SUPPORT_SERVER]

ORDER_CHANNEL = 1147897188366696568

active_tickets = {}

def is_guild_only():
    async def predicate(interaction: discord.Interaction):
        if not interaction.guild_id:
            await interaction.response.send_message("This command can only be used in servers!", ephemeral=True)
            return False
        return True
    return app_commands.check(predicate)

class TicketBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.all()
        intents.members = True  # Explicitly enable member intents
        super().__init__(command_prefix="!", intents=intents)
        self.persistent_views_added = False

bot = TicketBot()

async def create_new_ticket(message, support_guild):
    """Hilfsfunktion zum Erstellen eines neuen Tickets"""
    try:
        category = discord.utils.get(support_guild.categories, id=TICKET_CATEGORY)
        if not category:
            await message.author.send("Error: Support Category not found!")
            return

        channel_name = f"ticket-{message.author.name.lower()}"

        # Überprüfe auf existierende Channels mit diesem Namen
        existing_channel = discord.utils.get(category.channels, name=channel_name)
        if existing_channel:
            active_tickets[existing_channel.id] = message.author.id
            if message.content:
                # Create embed for existing channel
                embed = discord.Embed(
                    description=message.content,
                    color=0xdf5e0f,
                    timestamp=message.created_at
                )
                embed.set_author(
                    name=f"{message.author.name}",
                    icon_url=message.author.avatar.url if message.author.avatar else None
                )
                embed.set_footer(text=f"User ID: {message.author.id}")
                
                await existing_channel.send(embed=embed)

                # Handle attachments
                for attachment in message.attachments:
                    if any(attachment.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.webp']):
                        embed = discord.Embed(color=0xdf5e0f)
                        embed.set_author(
                            name=f"{message.author.name}",
                            icon_url=message.author.avatar.url if message.author.avatar else None
                        )
                        embed.set_footer(text=f"User ID: {message.author.id}")
                        embed.set_image(url=attachment.url)
                        await existing_channel.send(embed=embed)
        else:
            # Create new channel
            overwrites = {
                support_guild.default_role: discord.PermissionOverwrite(read_messages=False),
                message.author: discord.PermissionOverwrite(
                    read_messages=True,
                    send_messages=True,
                    read_message_history=True
                ),
                support_guild.me: discord.PermissionOverwrite(
                    read_messages=True,
                    send_messages=True,
                    manage_channels=True
                )
            }

            ticket_channel = await support_guild.create_text_channel(
                name=channel_name,
                category=category,
                overwrites=overwrites,
                topic=f"Ticket for {message.author.name}"
            )

            active_tickets[ticket_channel.id] = message.author.id
            
            await message.author.send(f"**Your ticket has been created. Please ask your question or describe your issue in detail, and I will forward your message to our Support Team.**")
            
            # Create initial ticket embed
            embed = discord.Embed(
                title="**New Ticket**",
                description=f"**A new ticket has been created via DM.**\nUser Display Name: {message.author.display_name}\nUser Name: {message.author.name}\nUser ID: {message.author.id}",
                color=0xdf5e0f
            )
            await ticket_channel.send(embed=embed)

            # Create embed for user's initial message
            if message.content:
                embed = discord.Embed(
                    description=message.content,
                    color=0xdf5e0f,
                    timestamp=message.created_at
                )
                embed.set_author(
                    name=f"{message.author.name}",
                    icon_url=message.author.avatar.url if message.author.avatar else None
                )
                embed.set_footer(text=f"User ID: {message.author.id}")
                await ticket_channel.send(embed=embed)

            # Handle attachments
            for attachment in message.attachments:
                if any(attachment.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.webp']):
                    embed = discord.Embed(color=0xdf5e0f)
                    embed.set_author(
                        name=f"{message.author.name}",
                        icon_url=message.author.avatar.url if message.author.avatar else None
                    )
                    embed.set_footer(text=f"User ID: {message.author.id}")
                    embed.set_image(url=attachment.url)
                    await ticket_channel.send(embed=embed)

    except Exception as e:
        await message.author.send("An error occurred. Please contact conti8099.")
        print(f"Error creating ticket via DM: {e}")

async def load_existing_tickets():
    """Loads existing ticket channels on startup"""
    print("\n=== Starting Ticket Loading Process ===")
    
    support_guild = bot.get_guild(SUPPORT_SERVER)
    if not support_guild:
        print("Error: Support Server not found!")
        return
    print(f"Found support guild: {support_guild.name}")

    category = discord.utils.get(support_guild.categories, id=TICKET_CATEGORY)
    if not category:
        print(f"Error: Ticket Category not found! ID: {TICKET_CATEGORY}")
        return
    print(f"Found category: {category.name}")

    # Clear existing tickets
    active_tickets.clear()
    
    # Process all channels in the category
    for channel in category.text_channels:
        if channel.name.startswith('ticket-'):
            print(f"Processing channel: {channel.name}")
            try:
                # Extract username from channel name
                username = channel.name.replace('ticket-', '')
                user_id = None
                
                # Suche in den letzten 50 Nachrichten nach einer Embed mit User ID
                async for message in channel.history(limit=50):
                    if message.embeds:
                        for embed in message.embeds:
                            if embed.description and "User ID:" in embed.description:
                                try:
                                    user_id = int(embed.description.split("User ID: ")[1].split("\n")[0])
                                    print(f"Found user ID {user_id} in embed for channel {channel.name}")
                                    break
                                except:
                                    continue
                    if user_id:
                        break

                # Wenn keine User ID in Embeds gefunden wurde, extrahiere sie aus dem Channel-Namen
                if not user_id:
                    # Erstelle einen temporären User für den Channel
                    active_tickets[channel.id] = 0  # Temporäre ID
                    print(f"No user ID found, added channel anyway: {channel.name}")
                else:
                    active_tickets[channel.id] = user_id
                    print(f"Added channel with user ID: {channel.name} -> {user_id}")

            except Exception as e:
                print(f"Error processing channel {channel.name}: {e}")
                # Füge den Channel trotzdem hinzu
                active_tickets[channel.id] = 0
                continue

    print(f"\nLoaded {len(active_tickets)} active tickets:")
    for channel_id, user_id in active_tickets.items():
        channel = bot.get_channel(channel_id)
        if channel:
            print(f"- {channel.name} -> User ID: {user_id if user_id != 0 else 'Unknown'}")
    print("=== Ticket Loading Complete ===\n")

class TicketButton(Button):
    def __init__(self):
        super().__init__(
            label="Create a Ticket",
            style=discord.ButtonStyle.danger,
            emoji="🎫",
            custom_id="ticket_button"
        )
    
    # Diese Methode muss eingerückt sein, um Teil der Klasse zu sein
    async def callback(self, interaction: discord.Interaction):
        try:
            support_guild = bot.get_guild(SUPPORT_SERVER)
            if not support_guild:
                await interaction.response.send_message("Support Server not found!", ephemeral=True)
                return

            category = discord.utils.get(support_guild.categories, id=TICKET_CATEGORY)
            if not category:
                await interaction.response.send_message("Support Category not found!", ephemeral=True)
                return

            channel_name = f"ticket-{interaction.user.name.lower()}"

            existing_channel = discord.utils.get(category.channels, name=channel_name)
            if existing_channel:
                await interaction.response.send_message(
                    "You already have an open ticket. Write me a DM message!", 
                    ephemeral=True
                )
                return

            overwrites = {
                support_guild.default_role: discord.PermissionOverwrite(read_messages=False),
                interaction.user: discord.PermissionOverwrite(
                    read_messages=True,
                    send_messages=True,
                    read_message_history=True
                ),
                support_guild.me: discord.PermissionOverwrite(
                    read_messages=True,
                    send_messages=True,
                    manage_channels=True
                )
            }

            ticket_channel = await support_guild.create_text_channel(
                name=channel_name,
                category=category,
                overwrites=overwrites,
                topic=f"Ticket for {interaction.user.name}"
            )

            active_tickets[ticket_channel.id] = interaction.user.id
            print(f"New ticket created: Channel ID {ticket_channel.id} for User ID {interaction.user.id}")
            #await debug_tickets()

            embed = discord.Embed(
                title="**Thansk for contacting Support**",
                description="Please ask you question or describe you issue in detail, and I will forwarnd your message to our Support Team..",
                color=0xdf5e0f
            )
            
            embed.set_image(url="https://media.discordapp.net/attachments/1096426641007317032/1386350209143148544/discordbanner.png?ex=685962bf&is=6858113f&hm=c5ad1fd164752a80f6c6d2126a2852aa1b7c3d2914d3d60c85de88e463fe9e02&=&format=webp&quality=lossless&width=256&height=80")
            embed.set_footer(
                text="Adrenalin © 2025 | Dominate the leaderboard’s",
                icon_url="https://media.discordapp.net/attachments/1096426641007317032/1386349984097632276/logo.png?ex=68596289&is=68581109&hm=1c763f136586efb7ae5382813b2574d85bd507056d1ca527bc9e9f55d2bc4586&=&format=webp&quality=lossless&width=400&height=400"
            )
            dm_channel = await interaction.user.create_dm()
            await dm_channel.send(embed=embed)
            
            embed = discord.Embed(
                title="**New Ticket**",
                description= f"**A new ticket has been created.**\nUser Display Name: {interaction.user.display_name}\nUser Name: {interaction.user.name}\nUser ID: {interaction.user.id}",
                color=0xdf5e0f
            )
            await ticket_channel.send(embed=embed)

            await interaction.response.send_message(
                "Ticket has been created. Please check your DMs.", 
                ephemeral=True
            )

        except discord.Forbidden:
            await interaction.response.send_message(
                "❌ I couldn't send you a DM! Please enable DMs for this server to create a ticket.", 
                ephemeral=True
            )
        except Exception as e:
            await interaction.response.send_message(
                "An error occurred. Please contact conti8099.", 
                ephemeral=True
            )
            print(f"Error creating ticket: {e}")

class TicketView(View):
    def __init__(self):
        super().__init__(timeout=None)
        self.add_item(TicketButton())

@bot.event
async def on_message(message):
    if message.author.bot:
        return

    # If message is in a ticket channel
    if message.channel.id in active_tickets:
        user_id = active_tickets[message.channel.id]
        user = await bot.fetch_user(user_id)
        
        if user:
            try:
                if message.content or message.attachments:
                    # Create embed for DM
                    embed = discord.Embed(
                        description=message.content if message.content else "",
                        color=0xdf5e0f,
                        timestamp=message.created_at
                    )
                    embed.set_author(
                        name=f"{message.author.name}",
                        icon_url=message.author.avatar.url if message.author.avatar else None
                    )
                    
                    # Add attachments
                    if message.attachments:
                        # Set first image as embed image
                        for attachment in message.attachments:
                            if any(attachment.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.webp']):
                                embed.set_image(url=attachment.url)
                                break

                    await user.send(embed=embed)

                    # Send additional attachments separately in embeds
                    for attachment in message.attachments[1:]:  # Skip first attachment if it was set as main image
                        if any(attachment.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.webp']):
                            embed = discord.Embed(color=0xdf5e0f)
                            embed.set_author(
                                name=f"{message.author.name}",
                                icon_url=message.author.avatar.url if message.author.avatar else None
                            )
                            embed.set_image(url=attachment.url)
                            await user.send(embed=embed)

            except discord.Forbidden:
                await message.channel.send("❌ Could not send DM to ticket creator!")
                print(f"Failed to send DM to user {user.id}")

    # If message is a DM
    elif isinstance(message.channel, discord.DMChannel):
        print(f"Processing DM from {message.author.id}")
        
        # Find ticket channel for this user
        ticket_channel_id = None
        for channel_id, user_id in active_tickets.items():
            if int(user_id) == int(message.author.id):
                ticket_channel_id = channel_id
                print(f"Found ticket channel {ticket_channel_id} for user {message.author.id}")
                break

        support_guild = bot.get_guild(SUPPORT_SERVER)
        if not support_guild:
            await message.author.send("Error: Support Server not found!")
            return

        if ticket_channel_id:
            channel = support_guild.get_channel(ticket_channel_id)
            if channel:
                print(f"Sending message to channel {channel.name}")
                try:
                    # Send text content if exists
                    if message.content:
                        embed = discord.Embed(
                            description=message.content,
                            color=0xdf5e0f,
                            timestamp=message.created_at
                        )
                        embed.set_author(
                            name=f"{message.author.name}",
                            icon_url=message.author.avatar.url if message.author.avatar else None
                        )
                        embed.set_footer(text=f"User ID: {message.author.id}")
                        await channel.send(embed=embed)

                    # Handle attachments if exists
                    if message.attachments:
                        for attachment in message.attachments:
                            if any(attachment.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.webp']):
                                img_embed = discord.Embed(
                                    color=0xdf5e0f,
                                    timestamp=message.created_at
                                )
                                img_embed.set_author(
                                    name=f"{message.author.name}",
                                    icon_url=message.author.avatar.url if message.author.avatar else None
                                )
                                img_embed.set_footer(text=f"User ID: {message.author.id}")
                                img_embed.set_image(url=attachment.url)
                                await channel.send(embed=img_embed)
                                print(f"Sent image attachment to channel: {attachment.filename}")

                except Exception as e:
                    print(f"Error sending message to channel: {e}")
                    await message.author.send("Error sending your message to support channel.")
            else:
                print(f"Channel {ticket_channel_id} not found, creating new ticket")
                await create_new_ticket(message, support_guild)
        else:
            print(f"No ticket found for user {message.author.id}, creating new ticket")
            await create_new_ticket(message, support_guild)

    await bot.process_commands(message)

@bot.tree.command(name="setup", description="Creates the ticket message")
@is_guild_only()
async def setup(interaction: discord.Interaction):
    try:
        # Check if command is used in a guild
        if not interaction.guild_id:
            await interaction.response.send_message("This command can only be used in servers!", ephemeral=True)
            return
            
        # Check if used in the correct channel
        if interaction.channel_id != TICKET_CHANNEL:
            await interaction.response.send_message("This command can only be used in the designated ticket channel!", ephemeral=True)
            return

        embed = discord.Embed(
            title="**Ticket Support**",
            description=">>> **Need assistance? We're here to help! Click the button below to create a ticket.**\n\n**Support Languages:**\n • English\n • Deutsch\n\n**Feel free to communicate in your preferred language from the list above.**\n\n*Our support team typically responds within a few hours. Thank you for your patience!*\n\n",
            color=0xdf5e0f
        )
        
        embed.set_image(url="https://media.discordapp.net/attachments/1096426641007317032/1386350209143148544/discordbanner.png?ex=685962bf&is=6858113f&hm=c5ad1fd164752a80f6c6d2126a2852aa1b7c3d2914d3d60c85de88e463fe9e02&=&format=webp&quality=lossless&width=256&height=80")
        embed.set_footer(
            text="Adrenalin © 2025 | Dominate the leaderboard’s",
            icon_url="https://media.discordapp.net/attachments/1096426641007317032/1386349984097632276/logo.png?ex=68596289&is=68581109&hm=1c763f136586efb7ae5382813b2574d85bd507056d1ca527bc9e9f55d2bc4586&=&format=webp&quality=lossless&width=400&height=400"
        )
        
        view = TicketView()
        await interaction.channel.send(embed=embed, view=view)
        await interaction.response.send_message("Setup successful!", ephemeral=True)
    
    except discord.errors.Forbidden:
        await interaction.response.send_message("I don't have permission to send messages or create embeds in this channel!", ephemeral=True)
    except Exception as e:
        print(f"Error in setup command: {e}")
        await interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)

@bot.tree.command(name="userinfo", description="Shows information about the ticket creator")
@is_guild_only()
async def userinfo(interaction: discord.Interaction):
    try:
        # Prüfe ob der Command in einem Ticket-Channel verwendet wird
        if interaction.channel.id not in active_tickets:
            await interaction.response.send_message("This command can only be used in ticket channels!", ephemeral=True)
            return

        # Hole den Ticket-Ersteller
        user_id = active_tickets[interaction.channel.id]
        
        # Überprüfe, ob user_id gültig ist
        if not user_id or user_id == 0:
            await interaction.response.send_message("No valid user ID found for this ticket. The ticket might be corrupted.", ephemeral=True)
            return
            
        try:
            user = await bot.fetch_user(user_id)
            if not user:
                await interaction.response.send_message("Could not find the ticket creator. The user might have left Discord.", ephemeral=True)
                return
        except discord.errors.NotFound:
            await interaction.response.send_message("User not found. The user might have been deleted from Discord.", ephemeral=True)
            return
        except Exception as e:
            await interaction.response.send_message(f"Error fetching user information: {str(e)}", ephemeral=True)
            return

        # Ticket-Informationen
        channel = interaction.channel
        created_at = channel.created_at
        
        embed = discord.Embed(
            title="Ticket Information",
            color=0xdf5e0f,
            timestamp=interaction.created_at
        )

        # User Information
        embed.add_field(
            name="User Information",
            value=f"**Display Name:** {user.display_name}\n"
                  f"**Username:** {user.name}\n"
                  f"**User ID:** {user.id}\n"
                  f"**Account Created:** <t:{int(user.created_at.timestamp())}:R>",
            inline=False
        )

        # Ticket Information
        embed.add_field(
            name="Ticket Information",
            value=f"**Ticket Channel:** {channel.name}\n"
                  f"**Created:** <t:{int(created_at.timestamp())}:R>\n"
                  f"**Status:** Active",
            inline=False
        )

        # Avatar als Thumbnail
        if user.avatar:
            embed.set_thumbnail(url=user.avatar.url)

        await interaction.response.send_message(embed=embed, ephemeral=True)
        
    except Exception as e:
        print(f"Error in userinfo command: {e}")
        await interaction.response.send_message(f"An error occurred while processing the command: {str(e)}", ephemeral=True)




@bot.event
async def on_ready():
    if not bot.persistent_views_added:
        bot.add_view(TicketView())
        bot.persistent_views_added = True
    
    # Synchronisiere Commands nur für erlaubte Server
    for guild_id in ALLOWED_SERVERS:
        guild = discord.Object(id=guild_id)
        bot.tree.copy_global_to(guild=guild)
        await bot.tree.sync(guild=guild)
        print(f"Slash Commands synced for Server {guild_id}!")

    print(f"Bot is online as {bot.user}")
    await load_existing_tickets()
    
    activities = [
        discord.Activity(name="Infinity 🔥", type=discord.ActivityType.playing),
        discord.Activity(name="https://adrenalin.sellsn.io/", type=discord.ActivityType.playing),
    ]
    @tasks.loop(seconds=3)  
    async def rotate_activity():
        activity = activities[rotate_activity.current_loop % len(activities)]
        await bot.change_presence(status=discord.Status.dnd, activity=activity)

    # Starte die Task
    rotate_activity.start()

bot.run('MTAyMTQ2NDI3OTM4MTE5Mjc2NQ.G3RW9e.YGcJI_lIRFxBFCCRLkbWkMpJQN90oSkVojaBlY')

# Test bot.run('MTAyMzUzMDY4NTcwNjgxMzUzMQ.GWamg1.9xHWbwSb1uIbK2ltYMZUh7UjqidT1BDln1L7FU')
# Main bot.run('MTAyMTQ2NDI3OTM4MTE5Mjc2NQ.G3RW9e.YGcJI_lIRFxBFCCRLkbWkMpJQN90oSkVojaBlY')