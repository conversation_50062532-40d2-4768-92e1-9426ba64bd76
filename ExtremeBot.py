import discord
from discord import app_commands
from discord.ext import tasks, commands
import datetime as dt
import pytz
import random
from discord.ui import <PERSON><PERSON>, View
import asyncio
import time
from typing import Literal, Tuple




intents = discord.Intents.default()
client = discord.Client(intents=intents)
Extreme = app_commands.CommandTree(client)


timezone = "Europe/Berlin"
guild_id1 = 894976581561384970
memeber_id = 1274051437286002780
cooldown = time.time()

class VerifyButton(discord.ui.Button):
    def __init__(self, label, emoji, callback):
        super().__init__(label=label, style=discord.ButtonStyle.grey, emoji=emoji, custom_id=callback)

    async def callback(self, interaction):
        if self.custom_id == "verify":
            await verified(interaction)

class VerifyButtonView(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)
        self.add_item(VerifyButton(label='Verify', emoji='<:logo4:1276850358492205056>', callback="verify"))

async def verified(interaction):
    role = discord.utils.get(interaction.guild.roles, id=memeber_id)
    await interaction.user.add_roles(role)
    embed = discord.Embed(description="Welcome to Extreme Masters Community", color=0x02aeec)
    await interaction.response.send_message(embed=embed, ephemeral=True)
    
@Extreme.command(name="verifymsg", description="admin command only.", guild=discord.Object(id=guild_id1))
async def verifymsg(interaction):
    if interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("OK, ich schicke die Ankündigung.", ephemeral=True)
        view = VerifyButtonView()
        title1 = '>>> We do not tolerate cheating, hacking, or any other illegal activity on our server. Please read the Discord Terms of Service and Community Guidelines carefully before joining our server. These guidelines are in place to ensure a safe and enjoyable experience for all members.\n\nCheating, hacking, carding, illigale promotions, ads and other illegal activities are not allowed on this server.\n\nhttps://discord.com/terms\nhttps://discord.com/guidelines\n\n'
        title2 = 'To get verified hit the verify button below!! '
        embed = discord.Embed(
        title="**Extreme Server Rules**",
        description=title1 + "\n" + title2,
        color=0x02aeec)
        embed.set_image(url="https://media.discordapp.net/attachments/1152674805447917721/1276849478598398015/Black_Blue_and_Neon_Pink_Modern_Tech_Electronics_and_Technology_X-Frame_Banner.png?ex=66cb063e&is=66c9b4be&hm=5e22dc1460ab9602acbe38ac83452bcd9c8d21c88c0e43a6a9a936d5c57e59ec&=&format=webp&quality=lossless&width=1440&height=112")
        embed.set_footer(text=f"Extreme Masters", icon_url="https://media.discordapp.net/attachments/1152674805447917721/1276850225079648287/logo4.png?ex=66cb06f0&is=66c9b570&hm=e655352c6a9650303793fe545125e3a7e4f88fe3e535119af55894234a9f0dc8&=&format=webp&quality=lossless&width=400&height=400")
        view.message = await interaction.channel.send(embed=embed, view=view)
    else:
        embed = discord.Embed(description="Sorry, you do not have the required role to use this command.", color=0x02aeec)
        await interaction.response.send_message(embed=embed, ephemeral=True)


@Extreme.command(name="aimmer", description="Embed command!", guild=discord.Object(id=guild_id1))
async def aimmer(interaction):
    if interaction.user.guild_permissions.administrator:
        await interaction.response.send_message(f"Done!", ephemeral=True) 
        title1 = '>>> **Aimer is a sophisticated, multi-functional gaming enhancement tool designed to elevate your gameplay experience across a variety of supported titles. This comprehensive suite offers a range of customizable features to fine-tune your performance and adapt to different gaming scenarios.**\n'
        title2 = '**Key Features:**\n**Advanced Recoil Control System:**\n> Customize horizontal and vertical recoil patterns with precision.\n> Adjustable correction delay for fine-tuning weapon behavior.\n> Dual recoil profiles allow quick switching between primary and secondary settings, ideal for different weapons or situations.\n> Auto-toggle feature for seamless transitions between recoil profiles.\n'
        title3 = '**Rapid Fire Functionality:**\n> Automate repetitive clicking actions with customizable fire rate.\n> Adjustable delay between shots for various weapon types.\n> Assign a specific activation key for quick engagement.\n'
        title4 = '**Dynamic Crosshair Overlay:**\n> Multiple crosshair styles: dot, cross, circle, and t-shape\n> Customizable colors, sizes, and thicknesses for optimal visibility.\n> Toggle crosshair display on/off as needed.\n'
        title5 = '**Real-Time Status Overlay:**\n> At-a-glance display of active features and current settings.\n> Transparent overlay for minimal interference with gameplay.\n> Automatically hides when not in supported games.\n'
        title6 = '**Comprehensive Profile Management:**\n> Save and load multiple configuration profiles.\n> Quick switching between profiles for different games or playstyles.\n> Name and organize profiles for easy reference.\n'
        title7 = '**Extensive Keybind Customization:**\n> Assign personalized keys for all major functions.\n> Customizable keys for toggling features, pausing/resuming, and exiting the application.\n> Separate keybinds for primary and secondary recoil patterns.\n'
        title8 = '**Game-Specific Activation:**\n> Automatically enables features only in supported games.\n> Seamless integration that does not interfere with non-gaming applications.\n'
        title9 = '**User-Friendly Interface:**\n> Intuitive, color-coded GUI for easy navigation and configuration.\n> Organized sections for recoil, rapid fire, crosshair, and general settings.\n> Real-time updates of settings without need for application restart.\n'
        title10 = '**Audio Feedback System:**\n> Optional sound cues for feature activation and profile changes.\n> Enhances user awareness of tool status without visual distractions.\n'
        title11 = '**Automatic Updates:**\n> Built-in update checker to ensure you are always using the latest version.\n> Seamless update process for minimal disruption.\n'
        title12 = '**Auto Bunny Hopping:**\n> Effortlessly perform bunny hops for enhanced movement and speed.\n> Customizable activation key for easy toggling during gameplay.\n'
        title13 = '**Auto Ping:**\n> Automatically ping enemies while shooting for improved team communication.\n> Customizable settings to fit your playstyle and game requirements.\n'
        title14 = '**Performance Optimization:**\n> Low resource usage to minimize impact on game performance.\n> Efficient code structure for rapid response times.\n\n**Aimer is meticulously designed to provide a comprehensive suite of gaming enhancements while maintaining an intuitive and user-friendly interface. Whether you are a casual gamer looking to improve your experience or a competitive player seeking that extra edge, Aimer offers the tools and customization options to elevate your gameplay to the next level. With its adaptive features and game-specific activation, Aimer seamlessly integrates into your gaming routine, providing the enhancements you need without interfering with your regular computer use.**\n'
        title15 = '**Purchase:**\n> Week license: 7.99€\n> Month license: 13.99€\n> Lifetime license: 79.99€\n[+] <#1056703440216133774>\n[+] https://extreme.bgng.io/\n'

        embed = discord.Embed(
            title="**Aimer: Advanced Gaming Enhancement Suite**",
            description=title1 + "\n" + title2 + "\n" + title3 + "\n" + title4 + "\n" + title5 + "\n" + title6 + "\n" + title7 + "\n" + title8 + "\n" + title9 + "\n" + title10 + "\n" + title11 + "\n" + title12 + "\n" + title13 + "\n" + title14 + "\n" + title15,
            color=0x02aeec)
        embed.set_image(url="https://media.discordapp.net/attachments/1152674805447917721/1278043951504556194/Screenshot_2024-08-27_192724.png?ex=66cf5eae&is=66ce0d2e&hm=434b77c633af85037ec720d4b132d9202536cf1e254a706a3c56ad2dbcfbff2d&=&format=webp&quality=lossless&width=1200&height=676")
        embed.set_footer(text=f"Extreme Masters", icon_url="https://media.discordapp.net/attachments/1152674805447917721/1276850225079648287/logo4.png?ex=66cb06f0&is=66c9b570&hm=e655352c6a9650303793fe545125e3a7e4f88fe3e535119af55894234a9f0dc8&=&format=webp&quality=lossless&width=400&height=400")

        await interaction.channel.send(embed=embed)
    else:
        embed = discord.Embed(description="Sorry, you do not have admin permissions to use this command.", color=0xFF0000)
        await interaction.response.send_message(embed=embed, ephemeral=True)


@client.event
async def on_ready():
    view = VerifyButtonView()
    client.add_view(view=view)
    await Extreme.sync(guild=discord.Object(id=guild_id1))

    
    activities = [
        discord.Activity(name="Extreme Masters", type=discord.ActivityType.playing),
        discord.Activity(name="Domination", type=discord.ActivityType.playing),
    ]

    print(f"Logged in as {client.user} (ID: {client.user.id})")

    @tasks.loop(seconds=3)  
    async def rotate_activity():
        activity = activities[rotate_activity.current_loop % len(activities)]
        await client.change_presence(status=discord.Status.online, activity=activity)

    rotate_activity.start()
    print("Bot is ready")
    
client.run("MTI0ODkwOTI0NDAxNTgzNzI2Nw.Gt0GYI.-abJ_Zu3CsKqIZbVwTwR6HQ1izXqHObioZ7dM4")
